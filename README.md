# OWU 模型列表初始化项目

<div align="center">

![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)
![Status](https://img.shields.io/badge/Status-Active-brightgreen.svg)

一个智能的AI模型数据处理工具，自动为AI模型匹配品牌图标、生成标签和描述信息。

</div>

## 📋 目录

- [项目概述](#-项目概述)
- [核心功能](#-核心功能)
- [项目架构](#-项目架构)
- [快速开始](#-快速开始)
- [详细使用](#-详细使用)
- [配置说明](#-配置说明)
- [开发指南](#-开发指南)
- [故障排除](#-故障排除)
- [贡献指南](#-贡献指南)
- [许可证](#-许可证)

## 🎯 项目概述

OWU模型列表初始化项目是一个专门用于处理AI模型数据的Python工具。它能够自动为各种AI模型（如GPT、Claude、Gemini、Qwen等）匹配对应的品牌图标，生成智能标签，并创建详细的模型描述信息。

### 主要特点

- 🎨 **智能图标匹配** - 基于模型名称和ID自动匹配品牌图标
- 🏷️ **自动标签生成** - 根据模型特性智能生成分类标签
- 📝 **描述信息生成** - 为模型自动生成详细的功能描述
- 🔍 **多策略匹配** - 支持精确匹配、厂商映射、关键词匹配和模糊匹配
- 📊 **详细统计报告** - 提供处理结果的完整统计信息
- 🛠️ **模块化设计** - 清晰的代码结构，易于维护和扩展

## ⚡ 核心功能

### 1. 图标匹配系统

- **多级匹配策略**：
  - 精确匹配：直接匹配模型名称
  - 厂商映射：基于预定义的厂商映射规则
  - 关键词匹配：提取模型名称中的关键词
  - 模糊匹配：使用相似度算法进行匹配

- **图标优先级**：
  - 优先选择 `-color.png` 彩色图标
  - 次选无后缀的标准图标
  - 支持SVG、PNG、WEBP多种格式

### 2. 智能标签生成

根据模型特性自动生成标签：
- **厂商标签**：OpenAI、Anthropic、Google、阿里等
- **功能标签**：多模态、文生图、搜索检索、推理思考等
- **特性标签**：免费、付费、实验性等

### 3. 描述信息生成

为缺少描述的模型自动生成：
- 基于模型名称和厂商信息
- 结合模型功能特性
- 提供简洁明了的功能说明

## 🏗️ 项目架构

```
owu_model_list_init/
├── README.md                    # 项目文档
├── issues/                      # 任务记录
│   └── 模型数据处理任务.md
├── lobe-icons/                  # Git子模块 - 图标库
│   ├── packages/
│   │   ├── static-png/         # PNG图标
│   │   ├── static-svg/         # SVG图标
│   │   └── static-webp/        # WEBP图标
│   └── README.md
├── model_processor/             # 核心处理程序
│   ├── main.py                 # 主程序入口
│   ├── config.py               # 配置文件和映射规则
│   ├── requirements.txt        # 依赖包列表
│   └── utils/                  # 工具模块
│       ├── __init__.py
│       ├── file_handler.py     # 文件操作工具
│       ├── git_handler.py      # Git子模块操作
│       ├── icon_matcher.py     # 智能图标匹配算法
│       ├── tag_generator.py    # 智能标签生成器
│       ├── description_generator.py  # 描述生成器
│       └── logger.py           # 统一日志系统
├── models-export-*.json         # 输入的模型数据文件
└── models-export-mod.json       # 处理后的输出文件
```

## 🚀 快速开始

### 环境要求

- Python 3.7+
- Git（用于子模块管理）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd owu_model_list_init
```

2. **初始化子模块**
```bash
git submodule update --init --recursive
```

3. **安装依赖**
```bash
cd model_processor
pip install -r requirements.txt
```

### 运行程序

```bash
cd model_processor
python main.py
```

程序将自动：
1. 检查并更新lobe-icons子模块
2. 查找最新的模型数据文件
3. 处理模型数据（匹配图标、生成标签和描述）
4. 输出处理结果到 `models-export-mod.json`

## 📖 详细使用

### 输入文件格式

程序会自动查找项目根目录下的 `models-export-{数字}.json` 文件，选择数字最大的文件作为输入。

输入文件应包含模型数组，每个模型对象包含：
```json
{
  "id": "model-id",
  "name": "模型名称",
  "meta": {
    "profile_image_url": "图标URL（可选）",
    "description": "模型描述（可选）",
    "tags": []
  }
}
```

### 输出文件格式

处理后的文件将保存为 `models-export-mod.json`，包含：
- 更新的图标URL
- 智能生成的标签
- 自动生成的描述信息

### 处理统计

程序运行完成后会显示详细的处理统计：
- 总处理模型数量
- 成功匹配图标数量
- 更新标签数量
- 生成描述数量
- 处理耗时
- 失败匹配的模型列表

## ⚙️ 配置说明

### 厂商映射配置

在 `config.py` 中可以配置厂商名称映射：

```python
VENDOR_MAPPING = {
    'gpt': 'openai',
    'claude': 'claude',
    'gemini': 'gemini',
    'qwen': 'qwen',
    # 添加更多映射...
}
```

### 图标配置

```python
ICON_BASE_PATH = "lobe-icons/packages/static-png/light"
ICON_BASE_URL = "https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light"
```

### 日志配置

```python
LOG_LEVEL = logging.INFO
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
```

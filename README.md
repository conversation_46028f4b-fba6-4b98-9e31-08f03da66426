# OWU 模型列表初始化项目

<div align="center">

![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)
![Status](https://img.shields.io/badge/Status-Active-brightgreen.svg)

一个智能的AI模型数据处理工具，自动为AI模型匹配品牌图标、生成标签和描述信息。

</div>

## 📋 目录

- [项目概述](#-项目概述)
- [核心功能](#-核心功能)
- [项目架构](#-项目架构)
- [快速开始](#-快速开始)
- [详细使用](#-详细使用)
- [配置说明](#-配置说明)
- [开发指南](#-开发指南)
- [故障排除](#-故障排除)
- [贡献指南](#-贡献指南)

## 🎯 项目概述

OWU模型列表初始化项目是一个专门用于处理AI模型数据的Python工具。它能够自动为各种AI模型（如GPT、Claude、Gemini、Qwen等）匹配对应的品牌图标，生成智能标签，并创建详细的模型描述信息。

### 主要特点

- 🎨 **智能图标匹配** - 基于模型名称和ID自动匹配品牌图标
- 🏷️ **自动标签生成** - 根据模型特性智能生成分类标签
- 📝 **描述信息生成** - 为模型自动生成详细的功能描述
- 🔍 **多策略匹配** - 支持精确匹配、厂商映射、关键词匹配和模糊匹配
- 📊 **详细统计报告** - 提供处理结果的完整统计信息
- 🛠️ **模块化设计** - 清晰的代码结构，易于维护和扩展

## ⚡ 核心功能

### 1. 图标匹配系统

- **多级匹配策略**：
  - 精确匹配：直接匹配模型名称
  - 厂商映射：基于预定义的厂商映射规则
  - 关键词匹配：提取模型名称中的关键词
  - 模糊匹配：使用相似度算法进行匹配

- **图标优先级**：
  - 优先选择 `-color.png` 彩色图标
  - 次选无后缀的标准图标
  - 支持SVG、PNG、WEBP多种格式

### 2. 智能标签生成

根据模型特性自动生成标签：

- **厂商标签**：OpenAI、Anthropic、Google、阿里等
- **功能标签**：多模态、文生图、搜索检索、推理思考等
- **特性标签**：免费、付费、实验性等

### 3. 描述信息生成

为缺少描述的模型自动生成：

- 基于模型名称和厂商信息
- 结合模型功能特性
- 提供简洁明了的功能说明

## 🏗️ 项目架构

```
owu_model_list_init/
├── README.md                    # 项目文档
├── issues/                      # 任务记录
│   └── 模型数据处理任务.md
├── lobe-icons/                  # Git子模块 - 图标库
│   ├── packages/
│   │   ├── static-png/         # PNG图标
│   │   ├── static-svg/         # SVG图标
│   │   └── static-webp/        # WEBP图标
│   └── README.md
├── model_processor/             # 核心处理程序
│   ├── main.py                 # 主程序入口
│   ├── config.py               # 配置文件和映射规则
│   ├── requirements.txt        # 依赖包列表
│   └── utils/                  # 工具模块
│       ├── __init__.py
│       ├── file_handler.py     # 文件操作工具
│       ├── git_handler.py      # Git子模块操作
│       ├── icon_matcher.py     # 智能图标匹配算法
│       ├── tag_generator.py    # 智能标签生成器
│       ├── description_generator.py  # 描述生成器
│       └── logger.py           # 统一日志系统
├── models-export-*.json         # 输入的模型数据文件
└── models-export-mod.json       # 处理后的输出文件
```

## 🚀 快速开始

### 环境要求

- Python 3.7+
- Git（用于子模块管理）

### 安装步骤

1. **克隆项目**

```bash
git clone <repository-url>
cd owu_model_list_init
```

2. **初始化子模块**

```bash
git submodule update --init --recursive
```

3. **安装依赖**

```bash
cd model_processor
pip install -r requirements.txt
```

### 运行程序

```bash
cd model_processor
python main.py
```

程序将自动：

1. 检查并更新lobe-icons子模块
2. 查找最新的模型数据文件
3. 处理模型数据（匹配图标、生成标签和描述）
4. 输出处理结果到 `models-export-mod.json`

## 📖 详细使用

### 输入文件格式

程序会自动查找项目根目录下的 `models-export-{数字}.json` 文件，选择数字最大的文件作为输入。

输入文件应包含模型数组，每个模型对象包含：

```json
{
  "id": "model-id",
  "name": "模型名称",
  "meta": {
    "profile_image_url": "图标URL（可选）",
    "description": "模型描述（可选）",
    "tags": []
  }
}
```

### 输出文件格式

处理后的文件将保存为 `models-export-mod.json`，包含：

- 更新的图标URL
- 智能生成的标签
- 自动生成的描述信息

### 处理统计

程序运行完成后会显示详细的处理统计：

- 总处理模型数量
- 成功匹配图标数量
- 更新标签数量
- 生成描述数量
- 处理耗时
- 失败匹配的模型列表

## ⚙️ 配置说明

### 厂商映射配置

在 `config.py` 中可以配置厂商名称映射：

```python
VENDOR_MAPPING = {
    'gpt': 'openai',
    'claude': 'claude',
    'gemini': 'gemini',
    'qwen': 'qwen',
    # 添加更多映射...
}
```

### 图标配置

```python
ICON_BASE_PATH = "lobe-icons/packages/static-png/light"
ICON_BASE_URL = "https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light"
```

### 日志配置

```python
LOG_LEVEL = logging.INFO
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
```

## 🔧 开发指南

### 代码结构说明

#### 主要模块

1. **main.py** - 程序入口
   - `ModelProcessor` 类：主要的处理器类
   - 负责协调各个工具模块的工作

2. **config.py** - 配置管理
   - 厂商映射规则
   - 图标路径配置
   - 日志配置

3. **utils/file_handler.py** - 文件操作
   - JSON文件读写
   - 文件路径处理
   - 错误处理

4. **utils/git_handler.py** - Git操作
   - 子模块初始化和更新
   - 路径验证

5. **utils/icon_matcher.py** - 图标匹配
   - 多策略匹配算法
   - 相似度计算
   - 图标文件查找

6. **utils/tag_generator.py** - 标签生成
   - 基于规则的标签生成
   - 厂商标签识别
   - 功能标签推断

7. **utils/description_generator.py** - 描述生成
   - 模板化描述生成
   - 基于模型特征的描述

8. **utils/logger.py** - 日志系统
   - 统一的日志配置
   - 多级别日志输出

### 扩展开发

#### 添加新的厂商映射

在 `config.py` 中的 `VENDOR_MAPPING` 字典中添加新的映射关系：

```python
VENDOR_MAPPING = {
    # 现有映射...
    'new_model_keyword': 'new_vendor_icon_name',
}
```

#### 自定义标签规则

在 `utils/tag_generator.py` 中修改标签生成逻辑：

```python
def generate_tags(self, model_data, icon_name):
    # 添加自定义标签逻辑
    custom_tags = self._generate_custom_tags(model_data)
    return base_tags + custom_tags
```

#### 添加新的匹配策略

在 `utils/icon_matcher.py` 中添加新的匹配方法：

```python
def custom_match(self, model_name, model_id):
    # 实现自定义匹配逻辑
    return MatchResult(matched=True, icon_name="custom", ...)
```

### 测试

#### 运行测试

```bash
cd model_processor
python -m pytest tests/
```

#### 手动测试

```bash
# 测试单个模型处理
python -c "
from main import ModelProcessor
processor = ModelProcessor()
processor.initialize()
result = processor.process_model({'name': 'gpt-4', 'id': 'gpt-4'})
print(result)
"
```

## 🐛 故障排除

### 常见问题

#### 1. 子模块初始化失败

**问题**：`lobe-icons子模块初始化失败`

**解决方案**：

```bash
# 手动初始化子模块
git submodule update --init --recursive
# 或者强制更新
git submodule update --remote --force
```

#### 2. 图标文件未找到

**问题**：大量模型无法匹配到图标

**解决方案**：

1. 检查 `lobe-icons` 子模块是否正确初始化
2. 验证图标文件路径：`lobe-icons/packages/static-png/light/`
3. 检查网络连接，确保可以访问图标CDN

#### 3. JSON文件格式错误

**问题**：`加载模型数据失败`

**解决方案**：

1. 验证JSON文件格式是否正确
2. 检查文件编码（应为UTF-8）
3. 使用JSON验证工具检查语法

#### 4. 权限问题

**问题**：无法写入输出文件

**解决方案**：

```bash
# 检查文件权限
ls -la models-export-mod.json
# 修改权限
chmod 644 models-export-mod.json
```

### 调试模式

启用详细日志输出：

```python
# 在config.py中修改
LOG_LEVEL = logging.DEBUG
```

或者在运行时设置：

```bash
export LOG_LEVEL=DEBUG
python main.py
```

### 性能优化

#### 大文件处理

对于包含大量模型的文件：

1. **分批处理**：修改 `process_models` 方法支持分批处理
2. **并行处理**：使用多线程处理模型数据
3. **缓存机制**：缓存图标匹配结果

```python
# 示例：分批处理
def process_models_batch(self, models_data, batch_size=100):
    for i in range(0, len(models_data), batch_size):
        batch = models_data[i:i+batch_size]
        # 处理批次
```

## 🤝 贡献指南

### 贡献方式

1. **Fork** 项目到你的GitHub账户
2. **创建** 功能分支 (`git checkout -b feature/AmazingFeature`)
3. **提交** 你的修改 (`git commit -m 'Add some AmazingFeature'`)
4. **推送** 到分支 (`git push origin feature/AmazingFeature`)
5. **创建** Pull Request

### 代码规范

- 遵循 PEP 8 Python代码规范
- 添加适当的注释和文档字符串
- 编写单元测试
- 确保代码通过所有测试

### 提交信息规范

```text
类型(范围): 简短描述

详细描述（可选）

相关Issue: #123
```

类型包括：

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 🙏 致谢

- [LobeHub](https://github.com/lobehub) - 提供优秀的图标库
- [Lobe Icons](https://github.com/lobehub/lobe-icons) - AI/LLM模型品牌图标集合

<div align="center">

**[⬆ 回到顶部](#owu-模型列表初始化项目)**

Made with ❤️ by AI Assistant

</div>
